# from app.process_tasks import process_plant_solar, process_plant_wind, process_plant_both
# import time
# from helper.utils import get_data_wind_solar, get_both_plant_pairs_from_csv
# from datetime import datetime, timedelta
# from helper.logger_setup import setup_logger


# # Set up logger
# logger = setup_logger('schedule_tasks', 'schedule_tasks.log')


# def scheduled_task_solar(yesterday):
#     """
#     Schedules sequential processing of solar plants for the given date to reduce RAM usage.
#     Reads plant and customer data from the provided CSV file.
#     """
#     logger.info(f"Scheduled solar tasks starting for {yesterday}.")

#     plant_customer_pairs = get_data_wind_solar("static/customer_data - Sheet1.csv", 'solar')

#     if not plant_customer_pairs:
#         logger.warning("No solar plants found in the CSV.")
#         return

#     report_type = 'SOLAR INTEGRUM'

#     # Process plants sequentially instead of concurrently
#     for plant, customer in plant_customer_pairs:
#         logger.info(f"Processing {plant} - {customer}")
#         try:
#             process_plant_solar(plant, customer, yesterday, report_type)
#             logger.info(f"Successfully processed {plant} - {customer}")
#         except Exception as e:
#             logger.error(f"Error processing plant {plant}: {e}", exc_info=True)

#     logger.info(f"Scheduled solar tasks executed for {yesterday}.")


# def scheduled_task_wind(yesterday):
#     """
#     Schedules sequential processing of wind plants for the given date to reduce RAM usage.
#     Reads plant and customer data from the provided CSV file.
#     """
#     logger.info(f"Scheduled wind tasks starting for {yesterday}.")

#     plant_customer_pairs = get_data_wind_solar("static/customer_data - Sheet1.csv", 'wind')

#     if not plant_customer_pairs:
#         logger.warning("No wind plants found in the CSV.")
#         return

#     report_type = 'WIND INTEGRUM'

#     # Process plants sequentially instead of concurrently
#     for plant, customer in plant_customer_pairs:
#         logger.info(f"Processing {plant} - {customer}")
#         try:
#             process_plant_wind(plant, customer, yesterday, report_type)
#             logger.info(f"Successfully processed {plant} - {customer}")
#         except Exception as e:
#             logger.error(f"Error processing plant {plant}: {e}", exc_info=True)

#     logger.info(f"Scheduled wind tasks executed for {yesterday}.")


# def scheduled_task_both(yesterday):
#     """
#     Schedules sequential processing of both plants for the given date to reduce RAM usage.
#     Reads plant and customer data from the provided CSV file.
#     """
#     logger.info(f"Scheduled both-plants tasks starting for {yesterday}.")

#     both_plant_pairs = get_both_plant_pairs_from_csv("static/customer_data - Sheet1.csv")

#     if not both_plant_pairs:
#         logger.warning("No both-plant pairs found in the CSV.")
#         return

#     report_type = 'INTEGRUM'

#     # Process plants sequentially instead of concurrently
#     for plant_solar, plant_wind, customer in both_plant_pairs:
#         logger.info(f"Processing {plant_solar} & {plant_wind} - {customer}")
#         try:
#             process_plant_both(plant_solar, plant_wind, customer, yesterday, report_type)
#             logger.info(f"Successfully processed {plant_solar} & {plant_wind} - {customer}")
#         except Exception as e:
#             logger.error(f"Error processing plants {plant_solar} & {plant_wind}: {e}", exc_info=True)

#     logger.info(f"Scheduled both-plants tasks executed for {yesterday}.")


# def get_yesterday():
#     """Get yesterday's date in YYYY-MM-DD format."""
#     return (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")


# def run_scheduler():
#     """
#     Run the scheduler for processing all plant types.
#     Can be run immediately or scheduled for daily execution.
#     """
#     start_time = time.time()
#     logger.info("Scheduler started running.")

#     print("Start Time:", time.ctime(start_time))

#     # Get yesterday's date for processing
#     yesterday = get_yesterday()
#     # yesterday = '2025-01-15'  # Uncomment for testing with specific date

#     # Process all plant types sequentially
#     scheduled_task_solar(yesterday)
#     scheduled_task_wind(yesterday)
#     scheduled_task_both(yesterday)

#     end_time = time.time()
#     execution_time = end_time - start_time
#     print(f"Execution Time: {execution_time:.2f} seconds")
#     logger.info(f"All scheduled tasks completed in {execution_time:.2f} seconds")

#     # Uncomment these lines to enable daily scheduled execution at 1:00 AM
#     # schedule.every().day.at("01:00").do(scheduled_task_solar, get_yesterday())
#     # schedule.every().day.at("01:00").do(scheduled_task_wind, get_yesterday())
#     # schedule.every().day.at("01:00").do(scheduled_task_both, get_yesterday())

#     # Keep the scheduler running (uncomment if using scheduled execution)
#     # while True:
#     #     schedule.run_pending()
#     #     time.sleep(60)





from app.process_tasks import process_plant_solar, process_plant_wind, process_plant_both
import schedule
import time
from helper.utils import get_data_wind_solar, get_both_plant_pairs_from_csv
from datetime import datetime, timedelta
from helper.logger_setup import setup_logger


logging = setup_logger('schedule_tasks', 'schedule_tasks.log')


# def scheduled_task_solar(yesterday):
#     """
#     Schedules sequential processing of solar plants for the given date to reduce RAM usage.
#     Reads plant and customer data from the provided CSV file.
#     """
#     logging.info(f"Scheduled solar tasks starting for {yesterday}.")

#     plant_customer_pairs = get_data_wind_solar("static\customer_data - Sheet1.csv", 'solar')

#     if not plant_customer_pairs:
#         logging.warning("No solar plants found in the CSV.")
#         return

#     report_type = 'SOLAR INTEGRUM'

#     # Process plants sequentially instead of concurrently
#     for plant, customer in plant_customer_pairs:
#         logging.info(f"Processing {plant} - {customer}")
#         try:
#             process_plant_solar(plant, customer, yesterday, report_type)
#             logging.info(f"Successfully processed {plant} - {customer}")
#         except Exception as e:
#             logging.error(f"Error processing plant {plant}: {e}", exc_info=True)

#     logging.info(f"Scheduled solar tasks executed for {yesterday}.")



# def scheduled_task_wind(yesterday):
#     """
#     Schedules sequential processing of wind plants for the given date to reduce RAM usage.
#     Reads plant and customer data from the provided CSV file.
#     """
#     logging.info(f"Scheduled wind tasks starting for {yesterday}.")

#     plant_customer_pairs = get_data_wind_solar("static\customer_data - Sheet1.csv", 'wind')

#     if not plant_customer_pairs:
#         logging.warning("No wind plants found in the CSV.")
#         return

#     report_type = 'WIND INTEGRUM'

#     # Process plants sequentially instead of concurrently
#     for plant, customer in plant_customer_pairs:
#         logging.info(f"Processing {plant} - {customer}")
#         try:
#             process_plant_wind(plant, customer, yesterday, report_type)
#             logging.info(f"Successfully processed {plant} - {customer}")
#         except Exception as e:
#             logging.error(f"Error processing plant {plant}: {e}", exc_info=True)

#     logging.info(f"Scheduled wind tasks executed for {yesterday}.")


# def scheduled_task_both(yesterday):
#     logging.info(f"Scheduled both-plants tasks starting for {yesterday}.")

#     both_plant_pairs = get_both_plant_pairs_from_csv("static\customer_data - Sheet1.csv")

#     if not both_plant_pairs:
#         logging.warning("No both-plant pairs found in the CSV.")
#         return

#     report_type = 'INTEGRUM'

#     # Process plants sequentially instead of concurrently
#     for plant_solar, plant_wind, customer in both_plant_pairs:
#         logging.info(f"Processing {plant_solar} & {plant_wind} - {customer}")
#         try:
#             process_plant_both(plant_solar, plant_wind, customer, yesterday, report_type)
#             logging.info(f"Successfully processed {plant_solar} & {plant_wind} - {customer}")
#         except Exception as e:
#             logging.error(f"Error processing plants {plant_solar} & {plant_wind}: {e}", exc_info=True)

#     logging.info(f"Scheduled both-plants tasks executed for {yesterday}.")



def scheduled_task_solar(yesterday):
    logging.info(f"Scheduled solar tasks starting for {yesterday}.")

    plantName = ['IN.INTE.KIDS', "IN.INTE.ANS1"]
    customerName = ['Kids Clinic', "ANS Paper Mill Pvt Ltd"]
    report_type = 'SOLAR INTEGRUM'

    for plant, customer in zip(plantName, customerName):
        try:
            logging.info(f"Processing solar for {plant} - {customer}")
            process_plant_solar(plant, customer, yesterday, report_type)
        except Exception as e:
            logging.error(f"Error in solar task for plant {plant}: {e}")

    logging.info(f"Scheduled solar tasks executed for {yesterday}.")


def scheduled_task_wind(yesterday):
    logging.info(f"Scheduled wind tasks starting for {yesterday}.")

    plantName = ['IN.INTE.SPFL', 'IN.INTE.ANSP']
    customerName = ['Sipani Fibers Ltd', 'ANS Paper Mill']
    report_type = 'WIND INTEGRUM'

    for plant, customer in zip(plantName, customerName):
        try:
            logging.info(f"Processing wind for {plant} - {customer}")
            process_plant_wind(plant, customer, yesterday, report_type)
        except Exception as e:
            logging.error(f"Error in wind task for plant {plant}: {e}")

    logging.info(f"Scheduled wind tasks executed for {yesterday}.")


def scheduled_task_both(yesterday):
    logging.info(f"Scheduled both-plants tasks starting for {yesterday}.")

    plant_name_wind = ['IN.INTE.SABE', 'IN.INTE.KSIP', 'IN.INTE.KPLW']
    plant_name_solar = ['IN.INTE.SAAB','IN.INTE.KSIS', 'IN.INTE.KPLS']
    customer_name = ['M/s SAA AB', 'Kyathi steels', 'Klene Paks Limited']
    report_type = 'INTEGRUM'

    for plant_solar, plant_wind, customer in zip(plant_name_solar, plant_name_wind, customer_name):
        try:
            logging.info(f"Processing both for {plant_solar} & {plant_wind} - {customer}")
            process_plant_both(plant_solar, plant_wind, customer, yesterday, report_type)
        except Exception as e:
            logging.error(f"Error in both-plant task for {plant_solar} & {plant_wind}: {e}")

    logging.info(f"Scheduled both-plants tasks executed for {yesterday}.")


def get_yesterday():
    return (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")




def run_scheduler():
    """
    Run the scheduler for processing all plant types.
    Can be run immediately or scheduled for daily execution.
    """
    logging.info("Scheduler started running.")


    # Get yesterday's date for processing
    yesterday = get_yesterday()
    # yesterday = '2025-01-15'  # Uncomment for testing with specific date

    # Process all plant types sequentially
    # scheduled_task_solar(yesterday)
    # scheduled_task_wind(yesterday)
    scheduled_task_both(yesterday)


    # Uncomment these lines to enable daily scheduled execution at 1:00 AM
    # schedule.every().day.at("01:00").do(scheduled_task_solar, get_yesterday())
    # schedule.every().day.at("01:00").do(scheduled_task_wind, get_yesterday())
    # schedule.every().day.at("01:00").do(scheduled_task_both, get_yesterday())

    # Keep the scheduler running (uncomment if using scheduled execution)
    # while True:
    #     schedule.run_pending()
    #     time.sleep(60)

